import { ReturnPrimeWebhookPayload, TransformedReturnPayload, WebhookResponse, ProcessingError, SupplierNotification, ReturnItem } from '../types';
import { SupplierMapper } from './SupplierMapper';
import { EmailService } from './EmailService';
import { ShopifyService } from './ShopifyService';
import { LoggingService } from './LoggingService';
import { IdempotencyService } from './IdempotencyService';
import { ReturnPrimeService } from './ReturnPrimeService';
import { EnhancedReturnPrimeService } from './EnhancedReturnPrimeService';
import { RetryManager } from '../utils/retry';
import logger from '../utils/logger';

export class ReturnProcessor {
  private supplierMapper: SupplierMapper;
  private emailService: EmailService;
  private shopifyService: ShopifyService;
  private loggingService: LoggingService;
  private idempotencyService: IdempotencyService;
  private returnPrimeService: ReturnPrimeService;
  private enhancedReturnService: EnhancedReturnPrimeService;

  constructor() {
    this.supplierMapper = new SupplierMapper();
    this.emailService = new EmailService();
    this.shopifyService = new ShopifyService();
    this.loggingService = new LoggingService();
    this.idempotencyService = new IdempotencyService();
    this.returnPrimeService = new ReturnPrimeService();
    this.enhancedReturnService = new EnhancedReturnPrimeService();
  }

  /**
   * Check if webhook has already been processed
   */
  public async checkIdempotency(idempotencyKey: string): Promise<boolean> {
    return await this.idempotencyService.checkProcessed(idempotencyKey);
  }

  /**
   * Enrich webhook payload with additional data from Return Prime API and Shopify vendor information
   */
  private async enrichWebhookData(payload: TransformedReturnPayload): Promise<TransformedReturnPayload> {
    try {
      // Get additional return details from Return Prime API
      const returnResult = await this.returnPrimeService.getReturn(payload.return_id);

      // Enrich items with vendor information from Shopify
      const enrichedItems = await this.enrichItemsWithVendorInfo(payload.items);

      let enrichedPayload: TransformedReturnPayload = {
        ...payload,
        items: enrichedItems
      };

      if (returnResult.success && returnResult.data) {
        const returnData = returnResult.data;

        // Enrich the payload with additional Return Prime data
        enrichedPayload = {
          ...enrichedPayload,
          customer_name: payload.customer_name || returnData.customer_name,
          notes: payload.notes || returnData.notes
        };

        logger.info('Webhook data enriched with Return Prime API data', {
          returnId: payload.return_id,
          enrichedFields: ['customer_name', 'return_type', 'notes']
        });
      } else {
        logger.warn('Could not enrich webhook data - Return Prime API call failed', {
          returnId: payload.return_id,
          error: returnResult.error
        });
      }

      logger.info('Webhook data enriched with vendor information', {
        returnId: payload.return_id,
        itemsWithVendor: enrichedItems.filter(item => item.vendor_name).length,
        totalItems: enrichedItems.length
      });

      return enrichedPayload;
    } catch (error) {
      logger.error('Error enriching webhook data', {
        error,
        returnId: payload.return_id
      });
      return payload;
    }
  }

  /**
   * Enrich return items with vendor information from Shopify
   */
  private async enrichItemsWithVendorInfo(items: ReturnItem[]): Promise<ReturnItem[]> {
    const enrichedItems: ReturnItem[] = [];

    for (const item of items) {
      let enrichedItem = { ...item };

      try {
        // Try to get vendor info using different approaches
        let vendor: string | undefined;

        // Approach 1: Use product_id if available
        if (item.product_id) {
          const productDetails = await this.shopifyService.getProductDetails(item.product_id);
          if (productDetails.success && productDetails.vendor) {
            vendor = productDetails.vendor;
            logger.debug('Got vendor from product details', {
              sku: item.sku,
              productId: item.product_id,
              vendor
            });
          }
        }

        // Approach 2: Use variant_id if product_id didn't work
        if (!vendor && item.variant_id) {
          const variantDetails = await this.shopifyService.getVariantDetails(item.variant_id);
          if (variantDetails.success && variantDetails.vendor) {
            vendor = variantDetails.vendor;
            logger.debug('Got vendor from variant details', {
              sku: item.sku,
              variantId: item.variant_id,
              vendor
            });
          }
        }

        // Add vendor to the item if found
        if (vendor) {
          enrichedItem.vendor_name = vendor;
          logger.info('Successfully enriched item with vendor', {
            sku: item.sku,
            vendor
          });
        } else {
          logger.warn('Could not determine vendor for item', {
            sku: item.sku,
            productId: item.product_id,
            variantId: item.variant_id
          });
        }
      } catch (error) {
        logger.error('Error enriching item with vendor info', {
          error,
          sku: item.sku,
          productId: item.product_id,
          variantId: item.variant_id
        });
      }

      enrichedItems.push(enrichedItem);
    }

    return enrichedItems;
  }

  /**
   * Main method to process return requests
   */
  public async processReturn(
    payload: TransformedReturnPayload,
    idempotencyKey: string
  ): Promise<WebhookResponse> {
    const errors: ProcessingError[] = [];
    let processedItems = 0;

    try {
      logger.info('Starting return processing', {
        returnId: payload.return_id,
        eventType: payload.event_type,
        itemCount: payload.items.length
      });

      // Enrich webhook data with Return Prime API data
      const enrichedPayload = await this.enrichWebhookData(payload);

      // Group items by supplier
      const supplierGroups = this.supplierMapper.groupItemsBySupplier(enrichedPayload.items);

      // Create supplier notifications
      const notifications = this.supplierMapper.createSupplierNotifications(
        supplierGroups,
        enrichedPayload.return_id,
        enrichedPayload.order_id,
        enrichedPayload.customer_email,
        enrichedPayload.customer_name,
        enrichedPayload.created_at
      );

      // Process each supplier notification
      for (const notification of notifications) {
        try {
          await this.processSupplierNotification(notification, enrichedPayload);
          processedItems += notification.items.length;
        } catch (error) {
          logger.error('Failed to process supplier notification', {
            error,
            supplier: notification.supplier,
            returnId: enrichedPayload.return_id
          });
          
          errors.push({
            code: 'SUPPLIER_PROCESSING_ERROR',
            message: `Failed to process notification for ${notification.supplier}`,
            details: { supplier: notification.supplier, error },
            retryable: true
          });
        }
      }

      // Handle exchange processing for approved returns
      if (payload.event_type === 'request.approved' && payload.exchange) {
        try {
          await this.processExchange(payload);
        } catch (error) {
          logger.error('Failed to process exchange', {
            error,
            returnId: payload.return_id
          });
          
          errors.push({
            code: 'EXCHANGE_PROCESSING_ERROR',
            message: 'Failed to process exchange in Shopify',
            details: { error },
            retryable: true
          });
        }
      }

      // Log all return entries
      await this.logReturnEntries(payload, notifications);

      // Mark as processed if successful
      if (errors.length === 0) {
        await this.idempotencyService.markProcessed(idempotencyKey, {
          returnId: payload.return_id,
          processedItems,
          timestamp: new Date().toISOString()
        });
      }

      const success = errors.length === 0;
      
      logger.info('Return processing completed', {
        returnId: payload.return_id,
        success,
        processedItems,
        errorCount: errors.length
      });

      return {
        success,
        message: success ? 'Return processed successfully' : 'Return processed with errors',
        processedItems,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      logger.error('Unexpected error in return processing', {
        error,
        returnId: payload.return_id
      });

      return {
        success: false,
        message: 'Internal processing error',
        errors: [{
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          retryable: true
        }]
      };
    }
  }

  /**
   * Process individual supplier notification
   */
  private async processSupplierNotification(
    notification: SupplierNotification,
    payload: TransformedReturnPayload
  ): Promise<void> {
    const retryManager = RetryManager.forEmail();

    // Send email notification to supplier
    await retryManager.executeWithRetry(
      async () => {
        const success = await this.emailService.sendSupplierNotification(notification);
        if (!success) {
          throw new Error('Email sending failed');
        }
      },
      `email-supplier-${notification.supplier}`,
      (error, attempt) => ({
        code: 'EMAIL_ERROR',
        message: `Failed to send email to ${notification.supplier}`,
        details: { error, attempt },
        retryable: true
      })
    );

    // Send internal review notification if required
    if (notification.requiresManualReview) {
      await retryManager.executeWithRetry(
        async () => {
          const success = await this.emailService.sendInternalReviewNotification(
            notification,
            `Manual review required for ${notification.supplier}`
          );
          if (!success) {
            throw new Error('Internal review email sending failed');
          }
        },
        `email-internal-review-${notification.supplier}`,
        (error, attempt) => ({
          code: 'INTERNAL_EMAIL_ERROR',
          message: 'Failed to send internal review notification',
          details: { error, attempt },
          retryable: true
        })
      );
    }
  }

  /**
   * Process exchange in Shopify
   */
  private async processExchange(payload: TransformedReturnPayload): Promise<void> {
    const retryManager = RetryManager.forShopify();

    await retryManager.executeWithRetry(
      async () => {
        const result = await this.shopifyService.processExchange(
          payload.order_id,
          payload.items,
          payload.customer_email,
          payload.return_id
        );

        if (!result.success) {
          throw new Error(result.error || 'Shopify exchange processing failed');
        }

        logger.info('Exchange processed successfully in Shopify', {
          returnId: payload.return_id,
          shopifyReturnId: result.shopifyReturnId
        });
      },
      `shopify-exchange-${payload.return_id}`,
      (error, attempt) => ({
        code: 'SHOPIFY_ERROR',
        message: 'Failed to process exchange in Shopify',
        details: { error, attempt },
        retryable: true
      })
    );
  }

  /**
   * Log return entries for all processed items
   */
  private async logReturnEntries(
    payload: TransformedReturnPayload,
    notifications: SupplierNotification[]
  ): Promise<void> {
    const retryManager = RetryManager.forLogging();

    await retryManager.executeWithRetry(
      async () => {
        const logEntries = notifications.flatMap(notification =>
          notification.items.map(item =>
            this.loggingService.createReturnLogEntry(
              payload.return_id,
              payload.order_id,
              item.sku,
              notification.supplier,
              item.qty,
              payload.status,
              payload.customer_email,
              item.reason,
              payload.event_type
            )
          )
        );

        const result = await this.loggingService.logReturnEntries(logEntries);
        
        if (!result.success) {
          throw new Error(`Logging failed: ${result.errors.join(', ')}`);
        }
      },
      `logging-${payload.return_id}`,
      (error, attempt) => ({
        code: 'LOGGING_ERROR',
        message: 'Failed to log return entries',
        details: { error, attempt },
        retryable: true
      })
    );
  }

  /**
   * Validate system configuration
   */
  public async validateConfiguration(): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // Validate supplier mapping
    const supplierValidation = this.supplierMapper.validateConfiguration();
    if (!supplierValidation.isValid) {
      errors.push(...supplierValidation.errors);
    }

    // Test email service
    try {
      const emailTest = await this.emailService.testEmailConfiguration();
      if (!emailTest) {
        errors.push('Email service configuration invalid');
      }
    } catch (error) {
      errors.push(`Email service test failed: ${error}`);
    }

    // Test Shopify service
    try {
      const shopifyTest = await this.shopifyService.testConnection();
      if (!shopifyTest) {
        errors.push('Shopify API connection failed');
      }
    } catch (error) {
      errors.push(`Shopify API test failed: ${error}`);
    }

    // Test logging service
    try {
      const loggingTest = await this.loggingService.testConfiguration();
      if (!loggingTest.googleSheets && !loggingTest.airtable) {
        errors.push('No logging service configured or working');
      }
    } catch (error) {
      errors.push(`Logging service test failed: ${error}`);
    }

    // Test Return Prime API service
    try {
      const returnPrimeTest = await this.returnPrimeService.testConnection();
      if (!returnPrimeTest) {
        errors.push('Return Prime API connection failed');
      }
    } catch (error) {
      errors.push(`Return Prime API test failed: ${error}`);
    }

    // Test idempotency service
    try {
      const idempotencyTest = await this.idempotencyService.testConnection();
      if (!idempotencyTest) {
        errors.push('In-memory idempotency service test failed');
      }
    } catch (error) {
      errors.push(`Idempotency service test failed: ${error}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Process Return Prime refund webhook using enhanced service
   */
  public async processRefundWebhook(
    payload: ReturnPrimeWebhookPayload,
    headers: Record<string, any>,
    idempotencyKey: string
  ): Promise<WebhookResponse> {
    try {
      // Check idempotency first
      const alreadyProcessed = await this.checkIdempotency(idempotencyKey);
      if (alreadyProcessed) {
        logger.info('Refund webhook already processed', { idempotencyKey });
        return {
          success: true,
          message: 'Webhook already processed',
          processedItems: 0
        };
      }

      // Process using enhanced service
      const result = await this.enhancedReturnService.processRefundWebhook(payload, headers);

      if (!result.success) {
        return {
          success: false,
          message: result.error || 'Failed to process refund webhook',
          errors: [{
            code: 'PROCESSING_ERROR',
            message: result.error || 'Unknown error',
            retryable: true
          }]
        };
      }

      const processedReturn = result.processedReturn!;

      // Mark as processed
      await this.idempotencyService.markProcessed(idempotencyKey);

      // Log the processing result
      await this.loggingService.logReturn({
        timestamp: new Date().toISOString(),
        return_id: processedReturn.returnId,
        order_id: processedReturn.orderId,
        sku: processedReturn.items.map(i => i.item.sku).join(', '),
        supplier: processedReturn.items.map(i => i.vendor || 'Unknown').join(', '),
        qty: processedReturn.items.reduce((sum, i) => sum + i.item.qty, 0),
        status: 'processed',
        customer_email: processedReturn.customerEmail,
        reason: processedReturn.items.map(i => i.item.reason).join(', '),
        event_type: 'refund',
        processed_at: new Date().toISOString()
      });

      logger.info('Refund webhook processed successfully', {
        returnId: processedReturn.returnId,
        processedItems: processedReturn.processedItems,
        notificationsSent: processedReturn.notificationsSent,
        errors: processedReturn.errors.length
      });

      return {
        success: true,
        message: 'Refund webhook processed successfully',
        processedItems: processedReturn.processedItems,
        errors: processedReturn.errors.length > 0 ? processedReturn.errors.map(error => ({
          code: 'NOTIFICATION_ERROR',
          message: error,
          retryable: false
        })) : undefined
      };
    } catch (error: any) {
      logger.error('Failed to process refund webhook', {
        error: error.message,
        idempotencyKey
      });

      return {
        success: false,
        message: 'Internal processing error',
        errors: [{
          code: 'INTERNAL_ERROR',
          message: error.message,
          retryable: true
        }]
      };
    }
  }

  /**
   * Test the enhanced workflow
   */
  public async testEnhancedWorkflow(): Promise<{
    success: boolean;
    tests: Record<string, boolean>;
    errors: string[];
  }> {
    return await this.enhancedReturnService.testWorkflow();
  }
}
