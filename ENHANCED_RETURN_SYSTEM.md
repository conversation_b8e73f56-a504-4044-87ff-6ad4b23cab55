# Enhanced Return Processing System

## Overview

This enhanced return processing system automatically handles Return Prime refund webhooks by:

1. **Processing Return Prime Webhooks**: Specifically handles refund events from Return Prime
2. **Vendor Matching**: Automatically matches returned products to their suppliers using Shopify data
3. **Supplier Notifications**: Sends detailed email notifications to suppliers with return information
4. **Vendor Database Management**: Maintains a JSON database of vendor contact information
5. **Automated Workflow**: Processes returns without manual intervention

## Key Features

### ✅ Vendor Privacy
- Vendor contact information is stored privately and not exposed to customers
- Metafields are used internally only for system processing

### ✅ Refund-Only Processing
- System only processes Return Prime webhooks for refund requests
- Other webhook types are logged but not processed

### ✅ Dynamic Vendor Lookup
- Uses Shopify REST API for efficient product-to-vendor mapping
- Caches vendor information to reduce API calls
- Automatically syncs vendor database with Shopify products

### ✅ Comprehensive Email Notifications
- Professional HTML email templates for suppliers
- Includes all return details, customer information, and next steps
- Respects vendor return policies and processing times

### ✅ Error Handling & Logging
- Comprehensive error handling and retry logic
- Detailed logging for debugging and monitoring
- Idempotency protection to prevent duplicate processing

## System Architecture

```
Return Prime Webhook → Enhanced Return Service → Vendor Lookup → Email Notification
                                ↓
                        Vendor Database (JSON)
                                ↓
                        Shopify API (Product/Vendor Data)
```

## Core Services

### 1. VendorDatabaseService
- Manages vendor contact information in JSON format
- Provides caching for performance
- Supports CRUD operations for vendor data

### 2. EnhancedReturnPrimeService
- Processes Return Prime webhooks specifically for refunds
- Coordinates vendor lookup and notification sending
- Handles the complete return processing workflow

### 3. SupplierNotificationService
- Sends professional email notifications to suppliers
- Uses HTML templates with comprehensive return details
- Supports batch notifications for multiple suppliers

### 4. Enhanced ShopifyService
- Efficient vendor lookup using REST API
- Batch processing for multiple products
- Automatic vendor database synchronization

## Configuration

### Environment Variables
```env
# Shopify Configuration
SHOPIFY_STORE_URL=your-store.myshopify.com
SHOPIFY_ACCESS_TOKEN=your_access_token
SHOPIFY_API_VERSION=2025-07

# Return Prime Configuration
RETURN_PRIME_API_URL=https://api.returnprime.com
RETURN_PRIME_ADMIN_ACCESS_TOKEN=your_admin_token

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>
```

### Vendor Database Structure
```json
{
  "lastUpdated": "2025-07-15T16:50:00.000Z",
  "totalVendors": 10,
  "vendors": {
    "Vendor Name": {
      "name": "Vendor Name",
      "priority": "HIGH|MEDIUM|LOW",
      "verified": true,
      "contact": {
        "email": "<EMAIL>",
        "phone": "******-0123",
        "website": "https://vendor.com",
        "address": { ... },
        "businessHours": "Mon-Fri 9AM-5PM",
        "timezone": "PST"
      },
      "returnPolicy": {
        "url": "https://vendor.com/returns",
        "timeLimit": "30 days",
        "requirements": "Original packaging",
        "restockingFee": "15%",
        "returnAddress": "123 Vendor St, City, State 12345"
      },
      "products": {
        "count": 25,
        "categories": ["Category1", "Category2"],
        "tags": ["tag1", "tag2"]
      },
      "automation": {
        "emailTemplate": "vendor-return",
        "requiresApproval": true,
        "autoRefund": false,
        "processingTime": "3-5 business days"
      },
      "notes": "Special handling instructions"
    }
  }
}
```

## API Endpoints

### Test Endpoints
- `GET /test/enhanced-workflow` - Test the complete workflow
- `GET /test/return-prime` - Test Return Prime API connection

### Webhook Endpoints
- `POST /webhook/return-prime` - Main Return Prime webhook processor
- `POST /webhook/return-prime-log` - Debug logging endpoint

### Data Endpoints
- `GET /return-prime/return/:returnId` - Get return details
- `GET /return-prime/order/:orderNumber` - Get order details

## Usage

### 1. Setup Vendor Database
The system automatically creates and maintains a vendor database based on Shopify products:

```javascript
const shopifyService = new ShopifyService();
await shopifyService.syncVendorDatabase();
```

### 2. Process Return Prime Webhooks
Configure Return Prime to send webhooks to your endpoint:

```
Webhook URL: https://yourdomain.com/webhook/return-prime
Events: request/refunded
```

### 3. Monitor Processing
Check logs and use test endpoints to monitor system health:

```bash
# Test the complete workflow
curl https://yourdomain.com/test/enhanced-workflow

# Test Return Prime connection
curl https://yourdomain.com/test/return-prime
```

## Testing

Run the comprehensive test suite:

```bash
node test-enhanced-return-system.js
```

This tests:
- Enhanced workflow functionality
- Return Prime API connectivity
- Vendor database integrity
- Webhook processing with sample data

## Workflow Example

1. **Customer submits refund request** via Return Prime
2. **Return Prime sends webhook** to your system
3. **System processes webhook**:
   - Validates it's a refund event
   - Extracts product and customer information
   - Looks up vendor information from Shopify
   - Retrieves vendor contact details from database
4. **System sends email notification** to supplier with:
   - Return details and customer information
   - Product information and return reasons
   - Vendor's return policy information
   - Next steps for processing
5. **System logs the transaction** for tracking and reporting

## Supplier Email Content

Suppliers receive professional emails containing:
- Return request details (ID, date, customer info)
- Product information (name, SKU, quantity, reason)
- Customer contact information
- Refund amount and method
- Vendor's return policy details
- Clear next steps for processing
- Contact information for questions

## Error Handling

The system includes comprehensive error handling:
- **Idempotency**: Prevents duplicate processing of webhooks
- **Retry Logic**: Automatic retries for transient failures
- **Graceful Degradation**: Continues processing other items if one fails
- **Detailed Logging**: All errors are logged with context
- **Fallback Notifications**: Internal notifications for unmatched vendors

## Monitoring & Maintenance

### Regular Tasks
1. **Monitor vendor database** for new suppliers
2. **Update vendor contact information** as needed
3. **Review processing logs** for errors or issues
4. **Test email delivery** periodically
5. **Sync vendor database** with Shopify changes

### Key Metrics to Monitor
- Webhook processing success rate
- Vendor match rate
- Email delivery success rate
- Processing time per return
- Error frequency and types

## Security Considerations

- Vendor contact information is private and not exposed to customers
- Webhook signatures should be verified (currently disabled for testing)
- Email credentials should be stored securely
- API tokens should have minimal required permissions
- Regular security audits of vendor data access

## Support

For issues or questions:
1. Check the logs for detailed error information
2. Use test endpoints to verify system health
3. Review vendor database for missing or incorrect information
4. Verify API credentials and connectivity
5. Test email configuration and delivery
