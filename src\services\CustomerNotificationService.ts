import nodemailer from 'nodemailer';
import { config } from '../config';
import logger from '../utils/logger';
import { ReturnItem } from '../types';

export interface CustomerNotificationData {
  returnId: string;
  orderId: string;
  orderNumber?: string;
  customerEmail: string;
  customerName?: string;
  items: ReturnItem[];
  requestType: 'return' | 'warranty' | 'exchange';
  submittedDate: string;
  estimatedProcessingTime?: string;
  trackingInfo?: {
    returnLabel?: string;
    trackingNumber?: string;
    carrierName?: string;
  };
  notes?: string;
}

export class CustomerNotificationService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.secure,
      auth: {
        user: config.email.user,
        pass: config.email.pass
      }
    });
  }

  /**
   * Send return/warranty processing confirmation to customer
   */
  public async notifyCustomer(
    notificationData: CustomerNotificationData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Generate beautiful email template
      const template = this.generateCustomerEmailTemplate(notificationData);
      
      // Send email
      const mailOptions = {
        from: config.email.from,
        to: notificationData.customerEmail,
        subject: template.subject,
        html: template.html,
        text: template.text,
        replyTo: config.email.from
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info('Customer notification sent successfully', {
        customerEmail: notificationData.customerEmail,
        returnId: notificationData.returnId,
        requestType: notificationData.requestType,
        messageId: result.messageId
      });

      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error: any) {
      logger.error('Failed to send customer notification', {
        customerEmail: notificationData.customerEmail,
        returnId: notificationData.returnId,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate beautiful 3D-style email template for customer
   */
  private generateCustomerEmailTemplate(data: CustomerNotificationData): {
    subject: string;
    html: string;
    text: string;
  } {
    const requestTypeDisplay = data.requestType.charAt(0).toUpperCase() + data.requestType.slice(1);
    const subject = `✅ Your ${requestTypeDisplay} Request #${data.returnId} is Being Processed`;
    
    // Generate items list
    const itemsHtml = data.items.map(item => `
      <div style="
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 20px;
        margin: 10px 0;
        border-left: 4px solid #28a745;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transform: translateZ(0);
      ">
        <div style="display: flex; align-items: center; margin-bottom: 10px;">
          <div style="
            width: 8px;
            height: 8px;
            background: linear-gradient(45deg, #28a745, #20c997);
            border-radius: 50%;
            margin-right: 12px;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
          "></div>
          <h4 style="margin: 0; color: #2c3e50; font-weight: 600;">${item.name}</h4>
        </div>
        <div style="color: #6c757d; font-size: 14px; line-height: 1.6;">
          <strong>SKU:</strong> ${item.sku}<br>
          <strong>Quantity:</strong> ${item.qty}<br>
          <strong>Reason:</strong> ${item.reason}
        </div>
      </div>
    `).join('');

    const itemsText = data.items.map(item => 
      `• ${item.name} (SKU: ${item.sku}) - Qty: ${item.qty} - Reason: ${item.reason}`
    ).join('\n');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${requestTypeDisplay} Request Confirmation</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
          
          .container {
            max-width: 600px;
            margin: 0 auto;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
          }
          
          .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            border-radius: 20px 20px 0 0;
            position: relative;
            overflow: hidden;
          }
          
          .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
          }
          
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
          }
          
          .header h1 {
            color: white;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
          }
          
          .status-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            position: relative;
            z-index: 1;
          }
          
          .content {
            background: white;
            padding: 40px 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
          }
          
          .info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border: 1px solid rgba(0,0,0,0.05);
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
          }
          
          .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
          }
          
          .info-row:last-child {
            border-bottom: none;
          }
          
          .info-label {
            font-weight: 600;
            color: #495057;
          }
          
          .info-value {
            color: #2c3e50;
            font-weight: 500;
          }
          
          .timeline {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #2196f3;
            box-shadow: 0 5px 20px rgba(33, 150, 243, 0.15);
          }
          
          .timeline-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
          }
          
          .timeline-dot {
            width: 12px;
            height: 12px;
            background: linear-gradient(45deg, #2196f3, #21cbf3);
            border-radius: 50%;
            margin-right: 15px;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.4);
          }
          
          .timeline-dot.completed {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4);
          }
          
          .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 0 0 20px 20px;
          }
          
          .cta-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            display: inline-block;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            transition: transform 0.3s ease;
          }
          
          .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
          }
        </style>
      </head>
      <body style="margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
        <div class="container">
          <div class="header">
            <h1>🎉 ${requestTypeDisplay} Request Received!</h1>
            <div class="status-badge">✅ Processing Started</div>
          </div>
          
          <div class="content">
            <div style="text-align: center; margin-bottom: 30px;">
              <h2 style="color: #2c3e50; margin: 0 0 10px 0;">Hi ${data.customerName || 'Valued Customer'}! 👋</h2>
              <p style="color: #6c757d; font-size: 16px; margin: 0;">
                Great news! We've received your ${data.requestType} request and our team is already working on it.
              </p>
            </div>

            <div class="info-card">
              <h3 style="margin-top: 0; color: #2c3e50; display: flex; align-items: center;">
                <span style="margin-right: 10px;">📋</span> Request Details
              </h3>
              <div class="info-row">
                <span class="info-label">Request ID:</span>
                <span class="info-value">#${data.returnId}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Order Number:</span>
                <span class="info-value">#${data.orderNumber || data.orderId}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Request Type:</span>
                <span class="info-value">${requestTypeDisplay}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Submitted:</span>
                <span class="info-value">${new Date(data.submittedDate).toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}</span>
              </div>
              ${data.estimatedProcessingTime ? `
              <div class="info-row">
                <span class="info-label">Estimated Processing:</span>
                <span class="info-value">${data.estimatedProcessingTime}</span>
              </div>
              ` : ''}
            </div>

            <div style="margin: 30px 0;">
              <h3 style="color: #2c3e50; display: flex; align-items: center; margin-bottom: 20px;">
                <span style="margin-right: 10px;">📦</span> Items in Your Request
              </h3>
              ${itemsHtml}
            </div>

            <div class="timeline">
              <h3 style="margin-top: 0; color: #2c3e50; display: flex; align-items: center;">
                <span style="margin-right: 10px;">⏱️</span> What Happens Next
              </h3>
              <div class="timeline-step">
                <div class="timeline-dot completed"></div>
                <span><strong>Request Received</strong> - We've got your ${data.requestType} request!</span>
              </div>
              <div class="timeline-step">
                <div class="timeline-dot completed"></div>
                <span><strong>Supplier Notified</strong> - We've contacted the relevant suppliers</span>
              </div>
              <div class="timeline-step">
                <div class="timeline-dot"></div>
                <span><strong>Review & Processing</strong> - Suppliers will review your request</span>
              </div>
              <div class="timeline-step">
                <div class="timeline-dot"></div>
                <span><strong>Resolution</strong> - You'll receive updates on the outcome</span>
              </div>
            </div>

            ${data.trackingInfo ? `
            <div class="info-card">
              <h3 style="margin-top: 0; color: #2c3e50; display: flex; align-items: center;">
                <span style="margin-right: 10px;">🚚</span> Shipping Information
              </h3>
              ${data.trackingInfo.returnLabel ? `
              <div class="info-row">
                <span class="info-label">Return Label:</span>
                <span class="info-value">${data.trackingInfo.returnLabel}</span>
              </div>
              ` : ''}
              ${data.trackingInfo.trackingNumber ? `
              <div class="info-row">
                <span class="info-label">Tracking Number:</span>
                <span class="info-value">${data.trackingInfo.trackingNumber}</span>
              </div>
              ` : ''}
              ${data.trackingInfo.carrierName ? `
              <div class="info-row">
                <span class="info-label">Carrier:</span>
                <span class="info-value">${data.trackingInfo.carrierName}</span>
              </div>
              ` : ''}
            </div>
            ` : ''}

            ${data.notes ? `
            <div style="
              background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
              border-radius: 12px;
              padding: 20px;
              margin: 25px 0;
              border-left: 4px solid #ffc107;
              box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
            ">
              <h4 style="margin-top: 0; color: #856404; display: flex; align-items: center;">
                <span style="margin-right: 10px;">💬</span> Additional Notes
              </h4>
              <p style="margin: 0; color: #856404;">${data.notes}</p>
            </div>
            ` : ''}

            <div style="text-align: center; margin: 40px 0;">
              <a href="mailto:${config.email.from}" class="cta-button">
                📧 Contact Support
              </a>
            </div>

            <div style="
              background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
              border-radius: 12px;
              padding: 20px;
              margin: 25px 0;
              border-left: 4px solid #17a2b8;
              text-align: center;
            ">
              <h4 style="margin-top: 0; color: #0c5460;">💡 Need Help?</h4>
              <p style="margin: 0; color: #0c5460;">
                If you have any questions about your ${data.requestType} request, 
                feel free to reach out to our support team. We're here to help!
              </p>
            </div>
          </div>
          
          <div class="footer">
            <h3 style="margin: 0 0 15px 0;">Thank You for Choosing Bake Buds! 🌟</h3>
            <p style="margin: 0; opacity: 0.9;">
              We appreciate your business and are committed to providing excellent service.
            </p>
            <p style="margin: 15px 0 0 0; font-size: 14px; opacity: 0.7;">
              This is an automated message from Bake Buds Return Processing System<br>
              If you have questions, reply to this email or contact ${config.email.from}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
🎉 ${requestTypeDisplay} Request Received!

Hi ${data.customerName || 'Valued Customer'}!

Great news! We've received your ${data.requestType} request and our team is already working on it.

REQUEST DETAILS:
- Request ID: #${data.returnId}
- Order Number: #${data.orderNumber || data.orderId}
- Request Type: ${requestTypeDisplay}
- Submitted: ${new Date(data.submittedDate).toLocaleDateString()}
${data.estimatedProcessingTime ? `- Estimated Processing: ${data.estimatedProcessingTime}` : ''}

ITEMS IN YOUR REQUEST:
${itemsText}

WHAT HAPPENS NEXT:
✅ Request Received - We've got your ${data.requestType} request!
✅ Supplier Notified - We've contacted the relevant suppliers
⏳ Review & Processing - Suppliers will review your request
⏳ Resolution - You'll receive updates on the outcome

${data.trackingInfo ? `
SHIPPING INFORMATION:
${data.trackingInfo.returnLabel ? `- Return Label: ${data.trackingInfo.returnLabel}` : ''}
${data.trackingInfo.trackingNumber ? `- Tracking Number: ${data.trackingInfo.trackingNumber}` : ''}
${data.trackingInfo.carrierName ? `- Carrier: ${data.trackingInfo.carrierName}` : ''}
` : ''}

${data.notes ? `
ADDITIONAL NOTES:
${data.notes}
` : ''}

NEED HELP?
If you have any questions about your ${data.requestType} request, 
feel free to reach out to our support team at ${config.email.from}

Thank you for choosing Bake Buds!
We appreciate your business and are committed to providing excellent service.

This is an automated message from Bake Buds Return Processing System.
    `;

    return {
      subject,
      html,
      text
    };
  }

  /**
   * Test email configuration
   */
  public async testEmailConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Customer email connection test successful');
      return true;
    } catch (error: any) {
      logger.error('Customer email connection test failed', { error: error.message });
      return false;
    }
  }
}
