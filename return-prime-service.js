const { createAdminApiClient } = require('@shopify/admin-api-client');
const fs = require('fs').promises;
const nodemailer = require('nodemailer');
require('dotenv').config();

class ReturnPrimeService {
  constructor() {
    // Shopify client (borrowed from test-shopify-connection.js)
    this.shopifyClient = createAdminApiClient({
      storeDomain: process.env.SHOPIFY_STORE_URL,
      accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
      apiVersion: process.env.SHOPIFY_API_VERSION || '2025-07'
    });

    // Email transporter
    this.emailTransporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });

    // Load vendor database
    this.vendorDatabase = null;
    this.loadVendorDatabase();
  }

  // Load vendor database from JSON file
  async loadVendorDatabase() {
    try {
      const data = await fs.readFile('./vendor-database.json', 'utf8');
      this.vendorDatabase = JSON.parse(data);
      console.log(`📋 Loaded ${this.vendorDatabase.totalVendors} vendors from database`);
    } catch (error) {
      console.error('❌ Error loading vendor database:', error.message);
      this.vendorDatabase = { vendors: {} };
    }
  }

  // Process Return Prime webhook
  async processReturnRequest(webhookPayload) {
    console.log('🔄 Processing Return Prime webhook...\n');
    
    try {
      // Extract return request data (nested structure)
      const returnRequest = webhookPayload.request || webhookPayload;
      
      console.log('📦 Return Request Details:');
      console.log(`   Return ID: ${returnRequest.id}`);
      console.log(`   Customer Email: ${returnRequest.customer?.email || 'Not provided'}`);
      console.log(`   Status: ${returnRequest.status}`);
      console.log(`   Created: ${returnRequest.created_at}`);
      
      // Process each line item
      const processedItems = [];
      const lineItems = returnRequest.line_items || [];
      
      for (const item of lineItems) {
        console.log(`\n📋 Processing item: ${item.name || item.title}`);
        
        // Get product details from Shopify
        const productDetails = await this.getProductDetails(item.product_id, item.variant_id);
        
        if (productDetails) {
          const vendorInfo = this.getVendorInfo(productDetails.vendor);
          
          const processedItem = {
            returnItem: item,
            productDetails: productDetails,
            vendorInfo: vendorInfo,
            needsVendorNotification: vendorInfo.verified
          };
          
          processedItems.push(processedItem);
          
          console.log(`   Vendor: ${productDetails.vendor}`);
          console.log(`   Vendor Verified: ${vendorInfo.verified ? '✅' : '❌'}`);
          
          // Send vendor notification if verified
          if (vendorInfo.verified) {
            await this.sendVendorNotification(returnRequest, processedItem);
          } else {
            console.log(`   ⚠️ Vendor not verified - manual processing required`);
          }
        } else {
          console.log(`   ❌ Could not fetch product details`);
        }
      }
      
      // Log return processing summary
      await this.logReturnProcessing(returnRequest, processedItems);
      
      return {
        success: true,
        returnId: returnRequest.id,
        itemsProcessed: processedItems.length,
        vendorNotificationsSent: processedItems.filter(item => item.needsVendorNotification).length,
        summary: processedItems
      };
      
    } catch (error) {
      console.error('❌ Error processing return request:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get product details from Shopify
  async getProductDetails(productId, variantId) {
    try {
      // First get variant details
      const variantQuery = `
        query($id: ID!) {
          productVariant(id: $id) {
            id
            title
            sku
            barcode
            price
            product {
              id
              title
              vendor
              handle
              tags
              productType
            }
          }
        }
      `;

      let gqlProductId = productId;
      let gqlVariantId = variantId;
      
      // Convert to GraphQL ID format if needed
      if (productId && !productId.startsWith('gid://')) {
        gqlProductId = `gid://shopify/Product/${productId}`;
      }
      if (variantId && !variantId.startsWith('gid://')) {
        gqlVariantId = `gid://shopify/ProductVariant/${variantId}`;
      }

      const variables = { id: gqlVariantId || gqlProductId };
      const response = await this.shopifyClient.request(variantQuery, { variables });
      
      if (response.data?.productVariant) {
        const variant = response.data.productVariant;
        return {
          productId: variant.product.id,
          variantId: variant.id,
          productTitle: variant.product.title,
          variantTitle: variant.title,
          vendor: variant.product.vendor,
          sku: variant.sku,
          barcode: variant.barcode,
          price: variant.price,
          handle: variant.product.handle,
          tags: variant.product.tags,
          productType: variant.product.productType
        };
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error fetching product details:', error.message);
      return null;
    }
  }

  // Get vendor information from local database
  getVendorInfo(vendorName) {
    if (!this.vendorDatabase || !vendorName) {
      return {
        name: 'Unknown',
        verified: false,
        contact: { email: '<EMAIL>' },
        returnPolicy: {},
        automation: { requiresApproval: true }
      };
    }

    const vendor = this.vendorDatabase.vendors[vendorName];
    if (vendor) {
      return vendor;
    }

    // Return default vendor info for unknown vendors
    return {
      name: vendorName,
      verified: false,
      contact: {
        email: `contact@${vendorName.toLowerCase().replace(/\s+/g, '')}.com`,
        phone: 'Contact needed'
      },
      returnPolicy: {
        timeLimit: 'Contact needed',
        requirements: 'Contact needed'
      },
      automation: {
        requiresApproval: true,
        processingTime: 'Contact needed'
      },
      notes: 'Vendor information needs verification'
    };
  }

  // Send email notification to vendor
  async sendVendorNotification(returnRequest, processedItem) {
    const { vendorInfo, productDetails, returnItem } = processedItem;
    
    console.log(`📧 Sending notification to ${vendorInfo.name}...`);
    
    try {
      const emailSubject = `Return Request #${returnRequest.id} - ${productDetails.productTitle}`;
      
      const emailBody = this.generateVendorEmailBody(returnRequest, processedItem);
      
      const mailOptions = {
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: vendorInfo.contact.email,
        subject: emailSubject,
        html: emailBody,
        attachments: []
      };

      // Add CC to store owner if configured
      if (process.env.STORE_OWNER_EMAIL) {
        mailOptions.cc = process.env.STORE_OWNER_EMAIL;
      }

      const result = await this.emailTransporter.sendMail(mailOptions);
      
      console.log(`   ✅ Email sent to ${vendorInfo.contact.email}`);
      console.log(`   📧 Message ID: ${result.messageId}`);
      
      return {
        success: true,
        messageId: result.messageId,
        recipient: vendorInfo.contact.email
      };
      
    } catch (error) {
      console.error(`   ❌ Failed to send email to ${vendorInfo.name}:`, error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Generate vendor email body
  generateVendorEmailBody(returnRequest, processedItem) {
    const { vendorInfo, productDetails, returnItem } = processedItem;
    
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background-color: #f4f4f4; padding: 20px; border-radius: 5px; }
            .content { padding: 20px; }
            .product-details { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .return-details { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .footer { background-color: #f4f4f4; padding: 15px; border-radius: 5px; font-size: 12px; }
            .highlight { background-color: #fff3cd; padding: 10px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>🔄 Return Request Notification</h2>
            <p><strong>Return ID:</strong> ${returnRequest.id}</p>
            <p><strong>Date:</strong> ${new Date(returnRequest.created_at).toLocaleDateString()}</p>
        </div>
        
        <div class="content">
            <h3>Dear ${vendorInfo.name} Team,</h3>
            
            <p>We have received a return request for one of your products sold through Bake Buds. Please review the details below and process according to your return policy.</p>
            
            <div class="product-details">
                <h4>📦 Product Information</h4>
                <p><strong>Product:</strong> ${productDetails.productTitle}</p>
                <p><strong>Variant:</strong> ${productDetails.variantTitle}</p>
                <p><strong>SKU:</strong> ${productDetails.sku || 'Not specified'}</p>
                <p><strong>Price:</strong> $${productDetails.price}</p>
                <p><strong>Quantity:</strong> ${returnItem.quantity || 1}</p>
            </div>
            
            <div class="return-details">
                <h4>🔄 Return Information</h4>
                <p><strong>Customer Email:</strong> ${returnRequest.customer?.email || 'Not provided'}</p>
                <p><strong>Return Reason:</strong> ${returnItem.reason || returnRequest.reason || 'Not specified'}</p>
                <p><strong>Return Status:</strong> ${returnRequest.status}</p>
                <p><strong>Customer Notes:</strong> ${returnItem.note || returnRequest.note || 'None provided'}</p>
            </div>
            
            <div class="highlight">
                <h4>⚡ Action Required</h4>
                <p><strong>Processing Time:</strong> ${vendorInfo.automation?.processingTime || 'As per your policy'}</p>
                <p><strong>Return Policy:</strong> <a href="${vendorInfo.returnPolicy?.url || '#'}">View Policy</a></p>
                <p><strong>Requirements:</strong> ${vendorInfo.returnPolicy?.requirements || 'As per your policy'}</p>
            </div>
            
            <h4>📋 Next Steps:</h4>
            <ol>
                <li>Review the return request details</li>
                <li>Contact the customer if additional information is needed</li>
                <li>Process the return according to your policy</li>
                <li>Update us on the return status</li>
            </ol>
        </div>
        
        <div class="footer">
            <p><strong>Vendor Contact Information:</strong></p>
            <p>Email: ${vendorInfo.contact.email}</p>
            <p>Phone: ${vendorInfo.contact.phone}</p>
            <p>Business Hours: ${vendorInfo.contact.businessHours || 'As per your schedule'}</p>
            
            <hr>
            <p>This email was automatically generated by the Bake Buds return processing system.</p>
            <p>If you have questions, please contact our support team.</p>
        </div>
    </body>
    </html>
    `;
  }

  // Log return processing for tracking
  async logReturnProcessing(returnRequest, processedItems) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      returnId: returnRequest.id,
      customerEmail: returnRequest.customer?.email,
      status: returnRequest.status,
      itemsCount: processedItems.length,
      vendorNotifications: processedItems.map(item => ({
        vendor: item.vendorInfo.name,
        product: item.productDetails.productTitle,
        notificationSent: item.needsVendorNotification,
        vendorEmail: item.vendorInfo.contact.email
      }))
    };

    try {
      // Append to log file
      const logFile = './return-processing-log.json';
      let logs = [];
      
      try {
        const existingLogs = await fs.readFile(logFile, 'utf8');
        logs = JSON.parse(existingLogs);
      } catch (error) {
        // File doesn't exist, start with empty array
      }
      
      logs.push(logEntry);
      
      // Keep only last 1000 entries
      if (logs.length > 1000) {
        logs = logs.slice(-1000);
      }
      
      await fs.writeFile(logFile, JSON.stringify(logs, null, 2));
      console.log(`📝 Return processing logged to ${logFile}`);
      
    } catch (error) {
      console.error('❌ Error logging return processing:', error.message);
    }
  }
}

module.exports = ReturnPrimeService;

// Example webhook server (if running directly)
if (require.main === module) {
  const express = require('express');
  const app = express();
  const port = process.env.PORT || 3000;

  // Middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true }));

  // Initialize service
  const returnService = new ReturnPrimeService();

  // Return Prime webhook endpoint
  app.post('/webhook/return-prime', async (req, res) => {
    console.log('🔔 Received Return Prime webhook');

    try {
      const result = await returnService.processReturnRequest(req.body);

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Return request processed successfully',
          returnId: result.returnId,
          itemsProcessed: result.itemsProcessed,
          vendorNotificationsSent: result.vendorNotificationsSent
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Error processing return request',
          error: result.error
        });
      }
    } catch (error) {
      console.error('❌ Webhook processing error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  });

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({ status: 'OK', service: 'Return Prime Webhook Service' });
  });

  // Start server
  app.listen(port, () => {
    console.log(`🚀 Return Prime webhook service running on port ${port}`);
    console.log(`📡 Webhook URL: http://localhost:${port}/webhook/return-prime`);
  });
}
