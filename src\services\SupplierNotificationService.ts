import nodemailer from 'nodemailer';
import { config } from '../config';
import logger from '../utils/logger';
import { VendorInfo } from './VendorDatabaseService';
import { ReturnItem } from '../types';

export interface SupplierNotificationData {
  returnId: string;
  orderId: string;
  orderNumber?: string;
  customerEmail: string;
  customerName?: string;
  customerAddress?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  items: ReturnItem[];
  totalRefundAmount?: number;
  refundMethod?: string;
  returnReason?: string;
  returnDate: string;
  notes?: string;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export class SupplierNotificationService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.secure,
      auth: {
        user: config.email.user,
        pass: config.email.pass
      }
    });
  }

  /**
   * Send return notification to supplier
   */
  public async notifySupplier(
    vendor: VendorInfo,
    notificationData: SupplierNotificationData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Generate email template
      const template = this.generateEmailTemplate(vendor, notificationData);
      
      // Send email
      const mailOptions = {
        from: config.email.from,
        to: vendor.contact.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
        replyTo: config.email.from
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info('Supplier notification sent successfully', {
        vendor: vendor.name,
        email: vendor.contact.email,
        returnId: notificationData.returnId,
        messageId: result.messageId
      });

      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error: any) {
      logger.error('Failed to send supplier notification', {
        vendor: vendor.name,
        email: vendor.contact.email,
        returnId: notificationData.returnId,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate beautiful 3D-style email template for supplier notification
   */
  public generateEmailTemplate(vendor: VendorInfo, data: SupplierNotificationData): EmailTemplate {
    const subject = `🚨 Return Request #${data.returnId} - ${vendor.name} Products - Action Required`;

    // Generate beautiful items cards
    const itemsHtml = data.items.map(item => `
      <div style="
        background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
        border-radius: 12px;
        padding: 20px;
        margin: 10px 0;
        border-left: 4px solid #e53e3e;
        box-shadow: 0 4px 15px rgba(229, 62, 62, 0.1);
        transform: translateZ(0);
      ">
        <div style="display: flex; align-items: center; margin-bottom: 10px;">
          <div style="
            width: 8px;
            height: 8px;
            background: linear-gradient(45deg, #e53e3e, #fc8181);
            border-radius: 50%;
            margin-right: 12px;
            box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
          "></div>
          <h4 style="margin: 0; color: #2d3748; font-weight: 600;">${item.name}</h4>
        </div>
        <div style="color: #4a5568; font-size: 14px; line-height: 1.6;">
          <strong>SKU:</strong> ${item.sku}<br>
          <strong>Quantity:</strong> ${item.qty}<br>
          <strong>Return Reason:</strong> <span style="color: #e53e3e; font-weight: 600;">${item.reason}</span>
        </div>
      </div>
    `).join('');

    const itemsText = data.items.map(item =>
      `- ${item.name} (SKU: ${item.sku}) - Qty: ${item.qty} - Reason: ${item.reason}`
    ).join('\n');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Return Request Notification - ${vendor.name}</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

          * {
            box-sizing: border-box;
          }

          body {
            margin: 0;
            padding: 15px;
            background: #f7fafc;
            min-height: 100vh;
          }

          @media (max-width: 600px) {
            body {
              padding: 5px;
            }
          }

          @media (prefers-color-scheme: dark) {
            body {
              background: #1a202c !important;
            }
          }

          .container {
            max-width: 500px;
            margin: 0 auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.4;
            color: #2d3748;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          }

          @media (max-width: 600px) {
            .container {
              margin: 0;
              max-width: 100%;
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
          }

          @media (prefers-color-scheme: dark) {
            .container {
              background: #2d3748 !important;
              color: #e2e8f0 !important;
              box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
            }
          }

          .header {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            padding: 20px 15px;
            text-align: center;
            position: relative;
            overflow: hidden;
          }

          @media (max-width: 600px) {
            .header {
              padding: 15px 10px;
            }
          }

          .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
          }

          @keyframes pulse {
            0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.7; }
            50% { transform: scale(1.1) rotate(180deg); opacity: 1; }
          }

          .header h1 {
            color: white;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
          }

          .urgent-badge {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            color: #c53030;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 700;
            display: inline-block;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(197, 48, 48, 0.3);
            position: relative;
            z-index: 1;
            border: 2px solid rgba(255,255,255,0.3);
          }

          .content {
            background: white;
            padding: 20px 15px;
          }

          @media (max-width: 600px) {
            .content {
              padding: 15px 10px;
            }
          }

          @media (prefers-color-scheme: dark) {
            .content {
              background: #2d3748 !important;
              color: #e2e8f0 !important;
            }
          }

          .info-card {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #f56565;
            box-shadow: 0 2px 8px rgba(245, 101, 101, 0.15);
          }

          @media (max-width: 600px) {
            .info-card {
              padding: 12px;
              margin: 10px 0;
              border-radius: 6px;
            }
          }

          @media (prefers-color-scheme: dark) {
            .info-card {
              background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%) !important;
              border-left: 4px solid #fc8181 !important;
              color: #e2e8f0 !important;
            }
          }

          .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
          }

          .info-row:last-child {
            border-bottom: none;
          }

          .info-label {
            font-weight: 600;
            color: #4a5568;
          }

          .info-value {
            color: #2d3748;
            font-weight: 500;
          }

          .priority-high {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            border-left: 4px solid #e53e3e;
            box-shadow: 0 5px 20px rgba(229, 62, 62, 0.15);
          }

          .priority-medium {
            background: linear-gradient(135deg, #fef5e7 0%, #fbd38d 100%);
            border-left: 4px solid #ed8936;
            box-shadow: 0 5px 20px rgba(237, 137, 54, 0.15);
          }

          .priority-low {
            background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
            border-left: 4px solid #38a169;
            box-shadow: 0 5px 20px rgba(56, 161, 105, 0.15);
          }

          .action-steps {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #319795;
            box-shadow: 0 5px 20px rgba(49, 151, 149, 0.15);
          }

          .step-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
          }

          .step-number {
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #319795, #4fd1c7);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 15px;
            box-shadow: 0 2px 8px rgba(49, 151, 149, 0.4);
          }

          .footer {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            padding: 25px 20px;
            text-align: center;
            border-radius: 0 0 15px 15px;
          }

          @media (max-width: 600px) {
            .footer {
              padding: 20px 15px;
              border-radius: 0 0 10px 10px;
            }
          }

          .cta-button {
            background: linear-gradient(135deg, #3182ce 0%, #2b6cb0 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            display: inline-block;
            margin: 20px 10px;
            box-shadow: 0 4px 15px rgba(49, 130, 206, 0.3);
            transition: transform 0.3s ease;
          }

          .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(49, 130, 206, 0.4);
          }

          .cta-button.urgent {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            box-shadow: 0 4px 15px rgba(229, 62, 62, 0.3);
          }

          .cta-button.urgent:hover {
            box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
          }
        </style>
      </head>
      <body style="margin: 0; padding: 20px; background: #f7fafc; min-height: 100vh;">
        <div class="container">
          <div class="header">
            <h1>🚨 Return Request Alert</h1>
            <div class="urgent-badge">⚡ IMMEDIATE ACTION REQUIRED</div>
          </div>

          <div class="content">
            <div style="text-align: center; margin-bottom: 30px;">
              <h2 style="color: #2d3748; margin: 0 0 10px 0;">Hello ${vendor.name} Team! 👋</h2>
              <p style="color: #4a5568; font-size: 16px; margin: 0;">
                A customer has submitted a return request for your products. Please review and take action promptly.
              </p>
            </div>

            <div class="info-card ${vendor.priority === 'HIGH' ? 'priority-high' : vendor.priority === 'MEDIUM' ? 'priority-medium' : 'priority-low'}">
              <h3 style="margin-top: 0; color: #2d3748; display: flex; align-items: center;">
                <span style="margin-right: 10px;">📋</span> Return Request Details
                <span style="
                  background: ${vendor.priority === 'HIGH' ? 'linear-gradient(45deg, #e53e3e, #fc8181)' : vendor.priority === 'MEDIUM' ? 'linear-gradient(45deg, #ed8936, #f6ad55)' : 'linear-gradient(45deg, #38a169, #68d391)'};
                  color: white;
                  padding: 4px 12px;
                  border-radius: 12px;
                  font-size: 12px;
                  margin-left: auto;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                ">${vendor.priority} PRIORITY</span>
              </h3>
              <div class="info-row">
                <span class="info-label">Return ID:</span>
                <span class="info-value" style="font-family: monospace; background: #f7fafc; padding: 4px 8px; border-radius: 4px;">#${data.returnId}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Order ID:</span>
                <span class="info-value" style="font-family: monospace; background: #f7fafc; padding: 4px 8px; border-radius: 4px;">#${data.orderId}</span>
              </div>
              ${data.orderNumber ? `
              <div class="info-row">
                <span class="info-label">Order Number:</span>
                <span class="info-value" style="font-family: monospace; background: #f7fafc; padding: 4px 8px; border-radius: 4px;">#${data.orderNumber}</span>
              </div>
              ` : ''}
              <div class="info-row">
                <span class="info-label">Return Date:</span>
                <span class="info-value">${new Date(data.returnDate).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Customer:</span>
                <span class="info-value">
                  <strong>${data.customerName || 'N/A'}</strong><br>
                  <a href="mailto:${data.customerEmail}" style="color: #3182ce; text-decoration: none;">${data.customerEmail}</a>
                </span>
              </div>
              ${data.totalRefundAmount ? `
              <div class="info-row">
                <span class="info-label">Refund Amount:</span>
                <span class="info-value" style="font-size: 18px; font-weight: 700; color: #e53e3e;">$${data.totalRefundAmount.toFixed(2)}</span>
              </div>
              ` : ''}
              ${data.refundMethod ? `
              <div class="info-row">
                <span class="info-label">Refund Method:</span>
                <span class="info-value">${data.refundMethod}</span>
              </div>
              ` : ''}
            </div>

            <div style="margin: 30px 0;">
              <h3 style="color: #2d3748; display: flex; align-items: center; margin-bottom: 20px;">
                <span style="margin-right: 10px;">📦</span> Returned Items
                <span style="
                  background: linear-gradient(45deg, #667eea, #764ba2);
                  color: white;
                  padding: 4px 12px;
                  border-radius: 12px;
                  font-size: 12px;
                  margin-left: 10px;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                ">${data.items.length} ITEM${data.items.length > 1 ? 'S' : ''}</span>
              </h3>
              ${itemsHtml}
            </div>

            ${data.customerAddress ? `
            <div class="info-card">
              <h3 style="margin-top: 0; color: #2d3748; display: flex; align-items: center;">
                <span style="margin-right: 10px;">📍</span> Customer Address
              </h3>
              <div style="
                background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
                padding: 20px;
                border-radius: 12px;
                border-left: 4px solid #38a169;
                box-shadow: 0 4px 15px rgba(56, 161, 105, 0.1);
              ">
                <p style="margin: 0; color: #2d3748; font-weight: 500; line-height: 1.8;">
                  📍 ${data.customerAddress.street}<br>
                  🏙️ ${data.customerAddress.city}, ${data.customerAddress.state} ${data.customerAddress.zip}<br>
                  🌍 ${data.customerAddress.country}
                </p>
              </div>
            </div>
            ` : ''}

            ${data.notes ? `
            <div style="
              background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
              border-radius: 15px;
              padding: 25px;
              margin: 25px 0;
              border-left: 4px solid #f59e0b;
              box-shadow: 0 5px 20px rgba(245, 158, 11, 0.15);
            ">
              <h3 style="margin-top: 0; color: #92400e; display: flex; align-items: center;">
                <span style="margin-right: 10px;">💬</span> Customer Notes
              </h3>
              <div style="
                background: rgba(255,255,255,0.7);
                padding: 15px;
                border-radius: 8px;
                border: 1px solid rgba(245, 158, 11, 0.2);
              ">
                <p style="margin: 0; color: #92400e; font-style: italic;">"${data.notes}"</p>
              </div>
            </div>
            ` : ''}

            <div class="action-steps">
              <h3 style="margin-top: 0; color: #234e52; display: flex; align-items: center;">
                <span style="margin-right: 10px;">🎯</span> Action Required - Next Steps
              </h3>
              <div class="step-item">
                <div class="step-number">1</div>
                <span><strong>Review Details:</strong> Carefully examine the return request information above</span>
              </div>
              <div class="step-item">
                <div class="step-number">2</div>
                <span><strong>Verify Requirements:</strong> Check return reason against your return policy</span>
              </div>
              <div class="step-item">
                <div class="step-number">3</div>
                <span><strong>Customer Contact:</strong> Reach out if additional information is needed</span>
              </div>
              <div class="step-item">
                <div class="step-number">4</div>
                <span><strong>Process Return:</strong> Handle according to your established procedures</span>
              </div>
              <div class="step-item">
                <div class="step-number">5</div>
                <span><strong>Update Systems:</strong> Adjust inventory and internal records</span>
              </div>
            </div>

            <div class="info-card">
              <h3 style="margin-top: 0; color: #2d3748; display: flex; align-items: center;">
                <span style="margin-right: 10px;">📋</span> Your Return Policy Reference
              </h3>
              <div class="info-row">
                <span class="info-label">⏰ Time Limit:</span>
                <span class="info-value" style="font-weight: 600; color: #e53e3e;">${vendor.returnPolicy.timeLimit}</span>
              </div>
              <div class="info-row">
                <span class="info-label">📝 Requirements:</span>
                <span class="info-value">${vendor.returnPolicy.requirements}</span>
              </div>
              <div class="info-row">
                <span class="info-label">💰 Restocking Fee:</span>
                <span class="info-value" style="font-weight: 600;">${vendor.returnPolicy.restockingFee}</span>
              </div>
              <div class="info-row">
                <span class="info-label">📮 Return Address:</span>
                <span class="info-value" style="font-family: monospace; background: #f7fafc; padding: 4px 8px; border-radius: 4px;">${vendor.returnPolicy.returnAddress}</span>
              </div>
              ${vendor.returnPolicy.url !== 'Contact needed' ? `
              <div class="info-row">
                <span class="info-label">🔗 Policy URL:</span>
                <span class="info-value"><a href="${vendor.returnPolicy.url}" style="color: #3182ce; text-decoration: none;">View Full Policy</a></span>
              </div>
              ` : ''}
            </div>

            <div style="text-align: center; margin: 40px 0;">
              <a href="mailto:${data.customerEmail}" class="cta-button urgent">
                📧 Contact Customer
              </a>
              <a href="mailto:${config.email.from}" class="cta-button">
                🆘 Get Support
              </a>
            </div>

            <div style="
              background: linear-gradient(135d, #e6fffa 0%, #b2f5ea 100%);
              border-radius: 15px;
              padding: 25px;
              margin: 25px 0;
              border-left: 4px solid #319795;
              text-align: center;
              box-shadow: 0 5px 20px rgba(49, 151, 149, 0.15);
            ">
              <h4 style="margin-top: 0; color: #234e52; display: flex; align-items: center; justify-content: center;">
                <span style="margin-right: 10px;">⚡</span> Processing Time: ${vendor.automation.processingTime}
              </h4>
              <p style="margin: 0; color: #234e52;">
                ${vendor.automation.requiresApproval ?
                  '⚠️ This return requires your approval before processing' :
                  '✅ This return can be processed automatically'
                }
              </p>
            </div>
          </div>

          <div class="footer">
            <h3 style="margin: 0 0 15px 0;">🤝 Bake Buds Supplier Partnership</h3>
            <p style="margin: 0; opacity: 0.9;">
              Thank you for being a valued supplier partner. We appreciate your prompt attention to customer returns.
            </p>
            <p style="margin: 15px 0 0 0; font-size: 14px; opacity: 0.7;">
              This is an automated notification from Bake Buds Return Processing System<br>
              Questions? Contact our supplier support team at ${config.email.from}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
🚨 RETURN REQUEST ALERT - ${vendor.name}
⚡ IMMEDIATE ACTION REQUIRED

Hello ${vendor.name} Team!

A customer has submitted a return request for your products. Please review and take action promptly.

📋 RETURN REQUEST DETAILS (${vendor.priority} PRIORITY):
- Return ID: #${data.returnId}
- Order ID: #${data.orderId}
${data.orderNumber ? `- Order Number: #${data.orderNumber}\n` : ''}
- Return Date: ${new Date(data.returnDate).toLocaleDateString('en-US', {
  weekday: 'long',
  year: 'numeric',
  month: 'long',
  day: 'numeric'
})}
- Customer: ${data.customerName || 'N/A'} (${data.customerEmail})
${data.totalRefundAmount ? `- Refund Amount: $${data.totalRefundAmount.toFixed(2)}\n` : ''}
${data.refundMethod ? `- Refund Method: ${data.refundMethod}\n` : ''}

📦 RETURNED ITEMS (${data.items.length} ITEM${data.items.length > 1 ? 'S' : ''}):
${itemsText}

${data.customerAddress ? `
📍 CUSTOMER ADDRESS:
📍 ${data.customerAddress.street}
🏙️ ${data.customerAddress.city}, ${data.customerAddress.state} ${data.customerAddress.zip}
🌍 ${data.customerAddress.country}
` : ''}

${data.notes ? `
💬 CUSTOMER NOTES:
"${data.notes}"
` : ''}

🎯 ACTION REQUIRED - NEXT STEPS:
1. Review Details: Carefully examine the return request information above
2. Verify Requirements: Check return reason against your return policy
3. Customer Contact: Reach out if additional information is needed
4. Process Return: Handle according to your established procedures
5. Update Systems: Adjust inventory and internal records

📋 YOUR RETURN POLICY REFERENCE:
⏰ Time Limit: ${vendor.returnPolicy.timeLimit}
📝 Requirements: ${vendor.returnPolicy.requirements}
💰 Restocking Fee: ${vendor.returnPolicy.restockingFee}
📮 Return Address: ${vendor.returnPolicy.returnAddress}
${vendor.returnPolicy.url !== 'Contact needed' ? `🔗 Policy URL: ${vendor.returnPolicy.url}\n` : ''}

⚡ PROCESSING TIME: ${vendor.automation.processingTime}
${vendor.automation.requiresApproval ?
  '⚠️ This return requires your approval before processing' :
  '✅ This return can be processed automatically'
}

🤝 BAKE BUDS SUPPLIER PARTNERSHIP
Thank you for being a valued supplier partner. We appreciate your prompt attention to customer returns.

This is an automated notification from Bake Buds Return Processing System.
Questions? Contact our supplier support team at ${config.email.from}

Customer Email: ${data.customerEmail}
Support Email: ${config.email.from}
    `;

    return {
      subject,
      html,
      text
    };
  }

  /**
   * Send batch notifications to multiple suppliers
   */
  public async notifyMultipleSuppliers(
    notifications: Array<{
      vendor: VendorInfo;
      data: SupplierNotificationData;
    }>
  ): Promise<{
    success: boolean;
    results: Array<{
      vendor: string;
      success: boolean;
      messageId?: string;
      error?: string;
    }>;
  }> {
    const results: Array<{
      vendor: string;
      success: boolean;
      messageId?: string;
      error?: string;
    }> = [];

    try {
      // Process notifications in parallel with a reasonable concurrency limit
      const batchSize = 3;
      for (let i = 0; i < notifications.length; i += batchSize) {
        const batch = notifications.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async ({ vendor, data }) => {
          const result = await this.notifySupplier(vendor, data);
          return {
            vendor: vendor.name,
            success: result.success,
            messageId: result.messageId,
            error: result.error
          };
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Small delay between batches to avoid overwhelming email server
        if (i + batchSize < notifications.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      const successCount = results.filter(r => r.success).length;
      
      logger.info('Batch supplier notifications completed', {
        total: notifications.length,
        successful: successCount,
        failed: notifications.length - successCount
      });

      return {
        success: true,
        results
      };
    } catch (error: any) {
      logger.error('Failed to send batch supplier notifications', {
        error: error.message,
        totalNotifications: notifications.length
      });

      return {
        success: false,
        results
      };
    }
  }

  /**
   * Test email configuration
   */
  public async testEmailConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Email connection test successful');
      return true;
    } catch (error: any) {
      logger.error('Email connection test failed', { error: error.message });
      return false;
    }
  }
}
