import nodemailer from 'nodemailer';
import { config } from '../config';
import logger from '../utils/logger';
import { VendorInfo } from './VendorDatabaseService';
import { ReturnItem } from '../types';

export interface SupplierNotificationData {
  returnId: string;
  orderId: string;
  orderNumber?: string;
  customerEmail: string;
  customerName?: string;
  customerAddress?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  items: ReturnItem[];
  totalRefundAmount?: number;
  refundMethod?: string;
  returnReason?: string;
  returnDate: string;
  notes?: string;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export class SupplierNotificationService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.secure,
      auth: {
        user: config.email.user,
        pass: config.email.pass
      }
    });
  }

  /**
   * Send return notification to supplier
   */
  public async notifySupplier(
    vendor: VendorInfo,
    notificationData: SupplierNotificationData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Generate email template
      const template = this.generateEmailTemplate(vendor, notificationData);
      
      // Send email
      const mailOptions = {
        from: config.email.from,
        to: vendor.contact.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
        replyTo: config.email.from
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info('Supplier notification sent successfully', {
        vendor: vendor.name,
        email: vendor.contact.email,
        returnId: notificationData.returnId,
        messageId: result.messageId
      });

      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error: any) {
      logger.error('Failed to send supplier notification', {
        vendor: vendor.name,
        email: vendor.contact.email,
        returnId: notificationData.returnId,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate email template for supplier notification
   */
  private generateEmailTemplate(vendor: VendorInfo, data: SupplierNotificationData): EmailTemplate {
    const subject = `Return Request #${data.returnId} - ${vendor.name} Products`;
    
    // Generate items table
    const itemsHtml = data.items.map(item => `
      <tr>
        <td style="padding: 8px; border: 1px solid #ddd;">${item.name}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">${item.sku}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">${item.qty}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">${item.reason}</td>
      </tr>
    `).join('');

    const itemsText = data.items.map(item => 
      `- ${item.name} (SKU: ${item.sku}) - Qty: ${item.qty} - Reason: ${item.reason}`
    ).join('\n');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Return Request Notification</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
            Return Request Notification
          </h2>
          
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #e74c3c;">⚠️ Action Required</h3>
            <p>A customer has submitted a return request for your products. Please review the details below and take appropriate action.</p>
          </div>

          <h3 style="color: #2c3e50;">Return Details</h3>
          <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Return ID:</td>
              <td style="padding: 8px;">${data.returnId}</td>
            </tr>
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Order ID:</td>
              <td style="padding: 8px;">${data.orderId}</td>
            </tr>
            ${data.orderNumber ? `
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Order Number:</td>
              <td style="padding: 8px;">${data.orderNumber}</td>
            </tr>
            ` : ''}
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Return Date:</td>
              <td style="padding: 8px;">${new Date(data.returnDate).toLocaleDateString()}</td>
            </tr>
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Customer:</td>
              <td style="padding: 8px;">${data.customerName || 'N/A'} (${data.customerEmail})</td>
            </tr>
            ${data.totalRefundAmount ? `
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Refund Amount:</td>
              <td style="padding: 8px;">$${data.totalRefundAmount.toFixed(2)}</td>
            </tr>
            ` : ''}
            ${data.refundMethod ? `
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Refund Method:</td>
              <td style="padding: 8px;">${data.refundMethod}</td>
            </tr>
            ` : ''}
          </table>

          <h3 style="color: #2c3e50;">Returned Items</h3>
          <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <thead>
              <tr style="background-color: #3498db; color: white;">
                <th style="padding: 10px; border: 1px solid #ddd;">Product Name</th>
                <th style="padding: 10px; border: 1px solid #ddd;">SKU</th>
                <th style="padding: 10px; border: 1px solid #ddd;">Quantity</th>
                <th style="padding: 10px; border: 1px solid #ddd;">Reason</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
            </tbody>
          </table>

          ${data.customerAddress ? `
          <h3 style="color: #2c3e50;">Customer Address</h3>
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
            <p style="margin: 0;">
              ${data.customerAddress.street}<br>
              ${data.customerAddress.city}, ${data.customerAddress.state} ${data.customerAddress.zip}<br>
              ${data.customerAddress.country}
            </p>
          </div>
          ` : ''}

          ${data.notes ? `
          <h3 style="color: #2c3e50;">Additional Notes</h3>
          <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">
            <p style="margin: 0;">${data.notes}</p>
          </div>
          ` : ''}

          <h3 style="color: #2c3e50;">Next Steps</h3>
          <div style="background-color: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;">
            <ol style="margin: 0; padding-left: 20px;">
              <li>Review the return request details above</li>
              <li>Verify the return reason and product condition requirements</li>
              <li>Contact the customer if additional information is needed</li>
              <li>Process the return according to your return policy</li>
              <li>Update your inventory and systems as needed</li>
            </ol>
          </div>

          <h3 style="color: #2c3e50;">Your Return Policy</h3>
          <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Time Limit:</td>
              <td style="padding: 8px;">${vendor.returnPolicy.timeLimit}</td>
            </tr>
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Requirements:</td>
              <td style="padding: 8px;">${vendor.returnPolicy.requirements}</td>
            </tr>
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Restocking Fee:</td>
              <td style="padding: 8px;">${vendor.returnPolicy.restockingFee}</td>
            </tr>
            <tr>
              <td style="padding: 8px; background-color: #f1f2f6; font-weight: bold;">Return Address:</td>
              <td style="padding: 8px;">${vendor.returnPolicy.returnAddress}</td>
            </tr>
          </table>

          <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px; text-align: center;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
              This is an automated notification from the Bake Buds return processing system.<br>
              If you have questions, please contact us at ${config.email.from}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Return Request Notification - ${vendor.name}

⚠️ ACTION REQUIRED
A customer has submitted a return request for your products. Please review the details below and take appropriate action.

RETURN DETAILS:
- Return ID: ${data.returnId}
- Order ID: ${data.orderId}
${data.orderNumber ? `- Order Number: ${data.orderNumber}\n` : ''}
- Return Date: ${new Date(data.returnDate).toLocaleDateString()}
- Customer: ${data.customerName || 'N/A'} (${data.customerEmail})
${data.totalRefundAmount ? `- Refund Amount: $${data.totalRefundAmount.toFixed(2)}\n` : ''}
${data.refundMethod ? `- Refund Method: ${data.refundMethod}\n` : ''}

RETURNED ITEMS:
${itemsText}

${data.customerAddress ? `
CUSTOMER ADDRESS:
${data.customerAddress.street}
${data.customerAddress.city}, ${data.customerAddress.state} ${data.customerAddress.zip}
${data.customerAddress.country}
` : ''}

${data.notes ? `
ADDITIONAL NOTES:
${data.notes}
` : ''}

NEXT STEPS:
1. Review the return request details above
2. Verify the return reason and product condition requirements
3. Contact the customer if additional information is needed
4. Process the return according to your return policy
5. Update your inventory and systems as needed

YOUR RETURN POLICY:
- Time Limit: ${vendor.returnPolicy.timeLimit}
- Requirements: ${vendor.returnPolicy.requirements}
- Restocking Fee: ${vendor.returnPolicy.restockingFee}
- Return Address: ${vendor.returnPolicy.returnAddress}

This is an automated notification from the Bake Buds return processing system.
If you have questions, please contact us at ${config.email.from}
    `;

    return {
      subject,
      html,
      text
    };
  }

  /**
   * Send batch notifications to multiple suppliers
   */
  public async notifyMultipleSuppliers(
    notifications: Array<{
      vendor: VendorInfo;
      data: SupplierNotificationData;
    }>
  ): Promise<{
    success: boolean;
    results: Array<{
      vendor: string;
      success: boolean;
      messageId?: string;
      error?: string;
    }>;
  }> {
    const results: Array<{
      vendor: string;
      success: boolean;
      messageId?: string;
      error?: string;
    }> = [];

    try {
      // Process notifications in parallel with a reasonable concurrency limit
      const batchSize = 3;
      for (let i = 0; i < notifications.length; i += batchSize) {
        const batch = notifications.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async ({ vendor, data }) => {
          const result = await this.notifySupplier(vendor, data);
          return {
            vendor: vendor.name,
            success: result.success,
            messageId: result.messageId,
            error: result.error
          };
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Small delay between batches to avoid overwhelming email server
        if (i + batchSize < notifications.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      const successCount = results.filter(r => r.success).length;
      
      logger.info('Batch supplier notifications completed', {
        total: notifications.length,
        successful: successCount,
        failed: notifications.length - successCount
      });

      return {
        success: true,
        results
      };
    } catch (error: any) {
      logger.error('Failed to send batch supplier notifications', {
        error: error.message,
        totalNotifications: notifications.length
      });

      return {
        success: false,
        results
      };
    }
  }

  /**
   * Test email configuration
   */
  public async testEmailConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Email connection test successful');
      return true;
    } catch (error: any) {
      logger.error('Email connection test failed', { error: error.message });
      return false;
    }
  }
}
