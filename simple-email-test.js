require('dotenv').config();
const nodemailer = require('nodemailer');

class SimpleEmailTest {
  constructor() {
    this.testEmail = '<EMAIL>';
    
    // Create transporter with Gmail settings
    this.transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false, // Use TLS
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
  }

  /**
   * Generate beautiful customer email
   */
  generateCustomerEmail() {
    const subject = '✅ Your Return Request #TEST-12345 is Being Processed';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Return Request Confirmation</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

          * {
            box-sizing: border-box;
          }

          .container {
            max-width: 600px;
            margin: 0 auto;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
          }

          @media (max-width: 600px) {
            .container {
              margin: 10px;
              max-width: calc(100% - 20px);
              border-radius: 10px;
            }
          }
          
          .header {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            padding: 30px 20px;
            text-align: center;
            border-radius: 15px 15px 0 0;
            position: relative;
            overflow: hidden;
          }

          @media (max-width: 600px) {
            .header {
              padding: 20px 15px;
              border-radius: 10px 10px 0 0;
            }
          }
          
          .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
          }
          
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
          }
          
          .header h1 {
            color: white;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
          }
          
          .status-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            position: relative;
            z-index: 1;
          }
          
          .content {
            background: white;
            padding: 30px 25px;
          }

          @media (max-width: 600px) {
            .content {
              padding: 20px 15px;
            }
          }
          
          .info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(0,0,0,0.05);
            box-shadow: 0 3px 15px rgba(0,0,0,0.08);
          }

          @media (max-width: 600px) {
            .info-card {
              padding: 15px;
              margin: 15px 0;
              border-radius: 8px;
            }
          }
          
          .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 0 0 20px 20px;
          }
        </style>
      </head>
      <body style="margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
        <div class="container">
          <div class="header">
            <h1>🎉 Return Request Received!</h1>
            <div class="status-badge">✅ Processing Started</div>
          </div>
          
          <div class="content">
            <div style="text-align: center; margin-bottom: 30px;">
              <h2 style="color: #2c3e50; margin: 0 0 10px 0;">Hi Feranmi! 👋</h2>
              <p style="color: #6c757d; font-size: 16px; margin: 0;">
                Great news! We've received your return request and our team is already working on it.
              </p>
            </div>

            <div class="info-card">
              <h3 style="margin-top: 0; color: #2c3e50;">📋 Request Details</h3>
              <p><strong>Request ID:</strong> #TEST-12345</p>
              <p><strong>Order Number:</strong> #BEAUTIFUL-TEST-001</p>
              <p><strong>Request Type:</strong> Return</p>
              <p><strong>Submitted:</strong> ${new Date().toLocaleDateString()}</p>
              <p><strong>Estimated Processing:</strong> 3-5 business days</p>
            </div>

            <div style="margin: 30px 0;">
              <h3 style="color: #2c3e50;">📦 Items in Your Request</h3>
              <div style="background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%); border-radius: 12px; padding: 20px; margin: 10px 0; border-left: 4px solid #e53e3e; box-shadow: 0 4px 15px rgba(229, 62, 62, 0.1);">
                <h4 style="margin: 0 0 10px 0; color: #2d3748;">Vessel - Air [Emerald] Premium Vaporizer</h4>
                <p style="margin: 0; color: #4a5568;"><strong>SKU:</strong> vessel-air-emerald-001<br><strong>Quantity:</strong> 2<br><strong>Reason:</strong> Product arrived damaged</p>
              </div>
            </div>

            <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-radius: 15px; padding: 25px; margin: 25px 0; border-left: 4px solid #2196f3;">
              <h3 style="margin-top: 0; color: #2c3e50;">⏱️ What Happens Next</h3>
              <p>✅ <strong>Request Received</strong> - We've got your return request!</p>
              <p>✅ <strong>Supplier Notified</strong> - We've contacted the relevant suppliers</p>
              <p>⏳ <strong>Review & Processing</strong> - Suppliers will review your request</p>
              <p>⏳ <strong>Resolution</strong> - You'll receive updates on the outcome</p>
            </div>
          </div>
          
          <div class="footer">
            <h3 style="margin: 0 0 15px 0;">Thank You for Choosing Bake Buds! 🌟</h3>
            <p style="margin: 0; opacity: 0.9;">
              We appreciate your business and are committed to providing excellent service.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
🎉 Return Request Received!

Hi Feranmi!

Great news! We've received your return request and our team is already working on it.

REQUEST DETAILS:
- Request ID: #TEST-12345
- Order Number: #BEAUTIFUL-TEST-001
- Request Type: Return
- Submitted: ${new Date().toLocaleDateString()}
- Estimated Processing: 3-5 business days

ITEMS IN YOUR REQUEST:
- Vessel - Air [Emerald] Premium Vaporizer (SKU: vessel-air-emerald-001) - Qty: 2 - Reason: Product arrived damaged

WHAT HAPPENS NEXT:
✅ Request Received - We've got your return request!
✅ Supplier Notified - We've contacted the relevant suppliers
⏳ Review & Processing - Suppliers will review your request
⏳ Resolution - You'll receive updates on the outcome

Thank you for choosing Bake Buds!
We appreciate your business and are committed to providing excellent service.
    `;

    return { subject, html, text };
  }

  /**
   * Generate beautiful supplier email
   */
  generateSupplierEmail() {
    const subject = '🚨 Return Request #TEST-12345 - Vessel Products - Action Required';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Return Request Notification - Vessel</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
          
          .container {
            max-width: 600px;
            margin: 0 auto;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #2d3748;
          }
          
          .header {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            padding: 40px 30px;
            text-align: center;
            border-radius: 20px 20px 0 0;
            position: relative;
            overflow: hidden;
          }
          
          .header h1 {
            color: white;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
          }
          
          .urgent-badge {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            color: #c53030;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 700;
            display: inline-block;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(197, 48, 48, 0.3);
            position: relative;
            z-index: 1;
          }
          
          .content {
            background: white;
            padding: 40px 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
          }
          
          .info-card {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #e53e3e;
            box-shadow: 0 5px 20px rgba(229, 62, 62, 0.15);
          }
          
          .footer {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 0 0 20px 20px;
          }
        </style>
      </head>
      <body style="margin: 0; padding: 20px; background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%); min-height: 100vh;">
        <div class="container">
          <div class="header">
            <h1>🚨 Return Request Alert</h1>
            <div class="urgent-badge">⚡ IMMEDIATE ACTION REQUIRED</div>
          </div>
          
          <div class="content">
            <div style="text-align: center; margin-bottom: 30px;">
              <h2 style="color: #2d3748; margin: 0 0 10px 0;">Hello Vessel Team! 👋</h2>
              <p style="color: #4a5568; font-size: 16px; margin: 0;">
                A customer has submitted a return request for your products. Please review and take action promptly.
              </p>
            </div>

            <div class="info-card">
              <h3 style="margin-top: 0; color: #2d3748;">📋 Return Request Details <span style="background: linear-gradient(45deg, #e53e3e, #fc8181); color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px; margin-left: 10px;">HIGH PRIORITY</span></h3>
              <p><strong>Return ID:</strong> #TEST-12345</p>
              <p><strong>Order ID:</strong> #ORDER-67890</p>
              <p><strong>Return Date:</strong> ${new Date().toLocaleDateString()}</p>
              <p><strong>Customer:</strong> Feranmi Olawore (<EMAIL>)</p>
              <p><strong>Refund Amount:</strong> $179.98</p>
            </div>

            <div style="margin: 30px 0;">
              <h3 style="color: #2d3748;">📦 Returned Items</h3>
              <div style="background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%); border-radius: 12px; padding: 20px; margin: 10px 0; border-left: 4px solid #e53e3e; box-shadow: 0 4px 15px rgba(229, 62, 62, 0.1);">
                <h4 style="margin: 0 0 10px 0; color: #2d3748;">Vessel - Air [Emerald] Premium Vaporizer</h4>
                <p style="margin: 0; color: #4a5568;"><strong>SKU:</strong> vessel-air-emerald-001<br><strong>Quantity:</strong> 2<br><strong>Return Reason:</strong> <span style="color: #e53e3e; font-weight: 600;">Product arrived damaged - packaging was torn</span></p>
              </div>
            </div>

            <div style="background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%); border-radius: 15px; padding: 25px; margin: 25px 0; border-left: 4px solid #319795;">
              <h3 style="margin-top: 0; color: #234e52;">🎯 Action Required - Next Steps</h3>
              <p><strong>1.</strong> Review the return request information above</p>
              <p><strong>2.</strong> Verify the return reason against your return policy</p>
              <p><strong>3.</strong> Contact the customer if additional information is needed</p>
              <p><strong>4.</strong> Process the return according to your established procedures</p>
              <p><strong>5.</strong> Update your inventory and internal records</p>
            </div>
          </div>
          
          <div class="footer">
            <h3 style="margin: 0 0 15px 0;">🤝 Bake Buds Supplier Partnership</h3>
            <p style="margin: 0; opacity: 0.9;">
              Thank you for being a valued supplier partner. We appreciate your prompt attention to customer returns.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
🚨 RETURN REQUEST ALERT - Vessel
⚡ IMMEDIATE ACTION REQUIRED

Hello Vessel Team!

A customer has submitted a return request for your products. Please review and take action promptly.

📋 RETURN REQUEST DETAILS (HIGH PRIORITY):
- Return ID: #TEST-12345
- Order ID: #ORDER-67890
- Return Date: ${new Date().toLocaleDateString()}
- Customer: Feranmi Olawore (<EMAIL>)
- Refund Amount: $179.98

📦 RETURNED ITEMS:
- Vessel - Air [Emerald] Premium Vaporizer (SKU: vessel-air-emerald-001) - Qty: 2 - Reason: Product arrived damaged - packaging was torn

🎯 ACTION REQUIRED - NEXT STEPS:
1. Review the return request information above
2. Verify the return reason against your return policy
3. Contact the customer if additional information is needed
4. Process the return according to your established procedures
5. Update your inventory and internal records

🤝 BAKE BUDS SUPPLIER PARTNERSHIP
Thank you for being a valued supplier partner. We appreciate your prompt attention to customer returns.

Customer Email: <EMAIL>
Support Email: ${process.env.EMAIL_FROM}
    `;

    return { subject, html, text };
  }

  /**
   * Send test emails
   */
  async sendTestEmails() {
    console.log('🚀 Starting Beautiful Email Test...\n');
    console.log(`📧 Sending test emails to: ${this.testEmail}\n`);

    try {
      // Test connection first
      console.log('🔗 Testing email connection...');
      await this.transporter.verify();
      console.log('✅ Email connection successful!\n');

      // Send customer email
      console.log('📧 Sending beautiful customer email...');
      const customerEmail = this.generateCustomerEmail();
      
      const customerResult = await this.transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: this.testEmail,
        subject: customerEmail.subject,
        html: customerEmail.html,
        text: customerEmail.text
      });

      console.log('✅ Customer email sent successfully!');
      console.log(`📨 Message ID: ${customerResult.messageId}\n`);

      // Send supplier email
      console.log('📧 Sending beautiful supplier email...');
      const supplierEmail = this.generateSupplierEmail();
      
      const supplierResult = await this.transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: this.testEmail,
        subject: supplierEmail.subject,
        html: supplierEmail.html,
        text: supplierEmail.text
      });

      console.log('✅ Supplier email sent successfully!');
      console.log(`📨 Message ID: ${supplierResult.messageId}\n`);

      // Success summary
      console.log('🎉 SUCCESS! Both beautiful emails <NAME_EMAIL>\n');
      
      console.log('📧 You should receive:');
      console.log('   1. 🎨 Beautiful Customer Confirmation Email');
      console.log('      • Gradient headers with animations');
      console.log('      • 3D status badges and timeline');
      console.log('      • Professional typography and layout');
      console.log('      • Mobile-responsive design');
      
      console.log('\n   2. 🚨 Professional Supplier Alert Email');
      console.log('      • Urgent red gradient header');
      console.log('      • HIGH priority color coding');
      console.log('      • Step-by-step action guides');
      console.log('      • Interactive contact buttons');
      
      console.log('\n💡 Email Features:');
      console.log('   • CSS3 gradients and animations');
      console.log('   • Box shadows for 3D depth');
      console.log('   • Professional Inter font family');
      console.log('   • Priority-based color schemes');
      console.log('   • Mobile-responsive layouts');
      console.log('   • Interactive hover effects');

      return true;
    } catch (error) {
      console.log('❌ Email test failed:', error.message);
      
      if (error.code === 'EAUTH') {
        console.log('\n🔧 Email Authentication Issue:');
        console.log('   • Check Gmail app password is correct');
        console.log('   • Ensure 2-factor authentication is enabled');
        console.log('   • Verify EMAIL_USER and EMAIL_PASS in .env file');
        console.log('   • Make sure "Less secure app access" is disabled (use app passwords)');
      }
      
      return false;
    }
  }
}

// Run test
const tester = new SimpleEmailTest();
tester.sendTestEmails().then(success => {
  console.log(`\n🏁 Email test ${success ? 'completed successfully' : 'failed'}!`);
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('💥 Email test runner failed:', error.message);
  process.exit(1);
});
